---
type: "manual"
---

# Next.js 15+ Frontend Development Rules & Best Practices

This document outlines the standards for developing robust, maintainable, and production-ready Next.js 15+ frontends, optimized for seamless integration with FastAPI backends.

---

## 1. Code Style & General Practices

- **DRY & KISS:**  
  Always reuse components, hooks, and utility functions. Keep code concise and easy to follow.

- **Type Safety:**  
  Use TypeScript for all code. Strictly type props, state, context, and API responses.
  never use "any" type, component, functional scale types can stay within the same component, but globle scale types shall prepaired and managed under types dir in frontend to make sure type safety and reuseability
  for each component, code lines under 350 lines maximumly, for server actions or controling logic flow,ode lines under 300 lines maximumly to maintain good code review

- **ESLint & Prettier:**  
  Configure and enforce linting rules. Use Prettier for consistent formatting.  
  Set up `eslint-config-next`, `@typescript-eslint/eslint-plugin`, and Prettier in CI.

- **Folder Structure:**  
  Use a modular structure:

```
frontend/
├── src/
│   ├── app/
│   │   ├── (auth)/
│   │   │   ├── login/
│   │   │   │   ├── page.tsx
│   │   │   │   └── loading.tsx
│   │   │   ├── signup/
│   │   │   │   ├── page.tsx
│   │   │   │   └── loading.tsx
│   │   │   └── layout.tsx
│   │   ├── (dashboard)/
│   │   │   ├── page.tsx
│   │   │   ├── layout.tsx
│   │   │   └── loading.tsx
│   │   ├── api/
│   │   │   └── route.ts
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   └── not-found.tsx
│   ├── components/
│   │   ├── ui/
│   │   │   ├── button.tsx
│   │   │   ├── card.tsx
│   │   │   └── input.tsx
│   │   ├── layout/
│   │   │   ├── header.tsx
│   │   │   ├── footer.tsx
│   │   │   └── sidebar.tsx
│   │   └── shared/
│   │       ├── navbar.tsx
│   │       └── theme-toggle.tsx
│   ├── lib/
│   │   ├── utils.ts
│   │   └── db.ts
│   ├── hooks/
│   │   └── use-toast.ts
│   ├── types/
│   │   └── index.ts
│   └── actions/
│       └── auth.ts
├── public/
│   ├── images/
│   └── favicon.ico
├── components.json
├── next.config.mjs
├── tsconfig.json
├── tailwind.config.ts
└── package.json

```

- **Naming Conventions:**  
  Use PascalCase for components, camelCase for functions/variables.

- **Documentation:**  
  Document components, hooks, and utils with JSDoc or TSDoc.  
  Maintain a `/docs` folder for architecture notes and onboarding.

---

## 2. Data Fetching & API Integration

- **React Server Components (RSC):**  
  Leverage RSC for static/dynamic rendering and optimal performance.

- **API Communication:**

  - Prefer `fetch` or [SWR](https://swr.vercel.app/) for data fetching.
  - Use typed API clients—generate types from FastAPI’s OpenAPI spec (e.g., [openapi-typescript](https://github.com/drwpow/openapi-typescript)).
  - Centralize API functions in `/lib/api.ts`.

- **Error Handling:**  
  Always handle loading, error, and empty states in UI.

- **Environment Variables:**  
  Store secrets in `.env.local`. Never leak sensitive config to the client.

---

## 3. Authentication & Security

- **Auth Flows:**

  - build a customized authentication system for FastAPI + Next.js, following the best practices for security, session management, and authorization.
  - Use succint but fully functional, adaptable frontend auth(never third party auth lib) for JWT, session, and OAuth support, campatible with Fastapi backend.
  - Implement email verification (with resend, FastAPI backend).
  - Store tokens securely—prefer HTTPOnly cookies for sensitive auth data.

- **CSRF Protection:**  
  to be implemented with SSR/session-based auth.

- **Role-Based UI:**  
  Conditionally render components/routes by user role/permissions.

- **CORS:**  
  Ensure backend allows only trusted origins; never hardcode URLs.

---

## 4. State Management

- **Local State:**  
  Use React context, hooks, or [Zustand](https://zustand-demo.pmnd.rs/) for simple state.

- **Global State:**  
  For complex flows, consider [Redux Toolkit](https://redux-toolkit.js.org/) or [Recoil](https://recoiljs.org/).

- **Hydration:**  
  Avoid unnecessary hydration; prefer server components for static data.

---

## 5. Styling

- **CSS-in-JS:**  
  Use [Tailwind CSS](https://tailwindcss.com/) with [shadcn](https://ui.shadcn.com/) for scalable, maintainable styles.

- **Theme Support:**  
  Implement dark/light mode and accessibility features.

- **Global Styles:**  
  Store global styles in `/styles/globals.css` or `/app/layout.tsx`.

---

## 6. Routing & Navigation

- **App Router:**  
  Use Next.js 15+ `/app` directory routing for layouts, nested routes, loading/error states.

- **Dynamic Routes:**  
  Use `[param]` or `[[...slug]]` for dynamic segments.

- **Middleware:**  
  Use `/middleware.ts` for auth, logging, redirects, etc.

---

## 7. Testing

- **Unit & Integration Tests:**  
  Use [Jest](https://jestjs.io/) and [React Testing Library](https://testing-library.com/).

- **End-to-End (E2E):**  
  Use [Playwright](https://playwright.dev/) or [Cypress](https://www.cypress.io/).

- **Coverage:**  
  Target >90% coverage. Integrate with CI.

- **Test Structure:**
  ```
  /__tests__
    /components
    /pages
    /e2e
  ```

---

## 8. Performance & Optimization

- **Next.js built in Strengths:**  
  Flexible Routing: Comprehensive support for static, dynamic, nested, catch-all, grouped, and conditional routes enables complex app navigation and URL structures.
  File System-Based Structure: Intuitive project structure with file colocation, private folders, and organized layouts simplifies codebase management.
  Advanced Layouts: Root, nested, and parallel layouts allow reusable, scalable UI patterns.
  Rich Metadata & Navigation: Built-in metadata handling, active link management, and programmatic navigation improve SEO and user experience.
  Robust Error & Loading Handling: Fine-grained error boundaries, loading UIs, and recovery strategies enhance reliability and user feedback.
  API Route Handlers: Direct support for GET, POST, PATCH, DELETE, and dynamic route handlers enables seamless backend integration.
  Built-in Middleware & Caching: Middleware for request interception and caching optimizes security and performance.
  Versatile Rendering: Supports static, dynamic, client-side, server-side, and streaming rendering; React Server Components enable efficient data handling and composition.
  Data Fetching Patterns: Sequential, parallel, and optimistic data fetching from APIs or databases, with dedicated hooks for UI state management.
  Form Actions: Server and client form handling with hooks for status, state, and optimistic updates improves UX and developer productivity.
  Authentication & Authorization: Integrated workflows for authentication (Clerk), session management, route protection, RBAC, and component customization.
  Third-Party Integration: Easy incorporation of context providers and external packages.
  Deployment: Streamlined deployment process for production-ready applications.

- **Form Actions in Next.js (frontend) with a FastAPI backend:**
  For Next.js + FastAPI architectures, useActionState is generally the most powerful and versatile choice for form actions; combine with useOptimistic for instant UI feedback, especially on interactive features. Use useFormStatus for basic loading states when you don’t need fine-grained control.
  For most forms:
  Use useActionState for robust state management, error handling, and integrating FastAPI responses.
  For simple submit/feedback:
  Use useFormStatus for loading and status indication.
  For interactive, real-time UX:
  Combine useActionState and useOptimistic for the best user experience:
  Optimistically update UI (useOptimistic)
  Use useActionState to reconcile with real backend (FastAPI) result

- **Image Optimization:**  
  Use Next.js `<Image />` for responsive, lazy-loaded images.
- **Code Splitting:**  
  Use dynamic imports for large/lazy components.

- **Static Generation & SSR:**  
  Use `generateStaticParams`, server components, and caching for optimal page speed.

- **Bundle Analysis:**  
  Regularly run `next build && npm run analyze` and address large bundles.

---

## 9. Accessibility & UX

- **Accessibility:**  
  Use semantic HTML. Test with screen readers.  
  Validate with [axe](https://www.deque.com/axe/) or [eslint-plugin-jsx-a11y](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y).

- **Responsive Design:**  
  Ensure mobile, tablet, and desktop support.

- **Error Boundaries:**  
  Implement error boundaries for robust UX.

---

## 10. Deployment & CI/CD(to be implemented)

## 11. Interoperability with FastAPI Backend

- **OpenAPI Client Generation:**  
  Use OpenAPI-generated TypeScript types & clients for backend compatibility.

- **Auth Compatibility:**  
  Support both JWT and session-based flows as provided by FastAPI backend.

- **Consistent Error Handling:**  
  Expect and handle JSON error responses per FastAPI conventions.

- **Versioned APIs:**  
  Always consume backend APIs with `/api/v1` or `/api/v2` prefixes.

- **CORS:**  
  Backend must whitelist frontend domain; frontend must handle potential CORS errors gracefully.

---

## 12. Example References

- [Next.js App Router Docs](https://nextjs.org/docs/app/building-your-application/routing)
- [NextAuth.js Example](https://github.com/nextauthjs/next-auth-example)
- [Vercel Next.js Best Practices](https://vercel.com/guides/nextjs-best-practices)
- [OpenAPI TypeScript Client](https://github.com/drwpow/openapi-typescript)

---

## 13. Senior Developer Tips

- **Automate Everything:**  
  Use scripts for setup, build, test, and deploy.

- **Code Reviews:**  
  Enforce reviews for all PRs. Use GitHub Actions for auto-checks.

- **Monitor Performance:**  
  Use Lighthouse and Vercel Analytics.

- **Stay Updated:**  
  Regularly upgrade dependencies. Track breaking changes in Next.js releases.

---

> **Following these rules will help you build scalable, maintainable, and high-performance Next.js 15+ frontends fully compatible with modern FastAPI backends.**
